// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dgajdcsittrezfyjdidc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRnYWpkY3NpdHRyZXpmeWpkaWRjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDMzNjUzNTQsImV4cCI6MjA1ODk0MTM1NH0.8bkn5NZMbqj29KSNN_0suvxxxtyMyfj_qZgVNizXyn0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);